import { NextRequest, NextResponse } from 'next/server'
import { logger } from '@/lib/logging'

/**
 * API endpoint to receive client-side logs and forward them to Docker container logs
 */
export async function POST(request: NextRequest) {
  try {
    const logEntry = await request.json()
    
    // Validate log entry structure
    if (!logEntry.type || !logEntry.level) {
      return NextResponse.json(
        { error: 'Invalid log entry format' },
        { status: 400 }
      )
    }

    // Add client-side indicator to the log entry
    const enhancedLogEntry = {
      ...logEntry,
      source: 'frontend',
      userAgent: request.headers.get('user-agent'),
      clientIp: request.headers.get('x-forwarded-for') || 
                request.headers.get('x-real-ip') || 
                'unknown'
    }

    // Log to server console based on log level
    switch (logEntry.level) {
      case 'error':
        logger.error('Frontend Network Request Error', enhancedLogEntry)
        break
      case 'warn':
        logger.warn('Frontend Network Request Warning', enhancedLogEntry)
        break
      case 'info':
      default:
        logger.info('Frontend Network Request', enhancedLogEntry)
        break
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    // Don't log this error to avoid infinite loops
    return NextResponse.json(
      { error: 'Failed to process log entry' },
      { status: 500 }
    )
  }
}
